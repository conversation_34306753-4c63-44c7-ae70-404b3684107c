package ai.conrain.aigc.platform.service.component;

import ai.conrain.aigc.platform.service.enums.ClothTypeEnum;
import ai.conrain.aigc.platform.service.model.biz.agent.ShootStyleImageRecommendParam;
import ai.conrain.aigc.platform.service.model.vo.StyleImageRecommendResult;
import ai.conrain.aigc.platform.service.model.vo.ImageCaptionVO;

/**
 * 图像检索服务
 */
public interface ImageSearchService {

    /**
     * 计算图像打标向量
     *
     * @param imageCaption
     * @param skipSortEmbedding
     */
    void calcEmbeddingsByImageCaption(ImageCaptionVO imageCaption, ClothTypeEnum clothTypeEnum, boolean skipSortEmbedding);

    /**
     * 搜索并推荐
     * @param param
     * @return
     */
    StyleImageRecommendResult searchAndRecommend(ShootStyleImageRecommendParam param);
}
