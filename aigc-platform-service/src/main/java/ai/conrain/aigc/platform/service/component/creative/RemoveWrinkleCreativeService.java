package ai.conrain.aigc.platform.service.component.creative;

import ai.conrain.aigc.platform.service.component.creative.async.RemoveWrinkleAsyncExecutor;
import ai.conrain.aigc.platform.service.constants.CommonConstants;
import ai.conrain.aigc.platform.service.enums.CreativeStatusEnum;
import ai.conrain.aigc.platform.service.enums.CreativeTypeEnum;
import ai.conrain.aigc.platform.service.helper.BatchFillHelper;
import ai.conrain.aigc.platform.service.helper.RemoveWrinkleTaskHelper;
import ai.conrain.aigc.platform.service.model.request.RemoveWrinkleRequest;
import ai.conrain.aigc.platform.service.model.vo.*;
import ai.conrain.aigc.platform.service.util.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.Date;
import java.util.Objects;

import static ai.conrain.aigc.platform.service.constants.CommonConstants.*;

@Service
public class RemoveWrinkleCreativeService extends AbstractCreativeService<RemoveWrinkleRequest> {

    @Autowired
    private BatchFillHelper batchFillHelper;
    @Autowired
    private RemoveWrinkleTaskHelper removeWrinkleTaskHelper;
    @Autowired
    private RemoveWrinkleAsyncExecutor removeWrinkleAsyncExecutor;

    @Override
    public CreativeTypeEnum getCreativeType() {
        return CreativeTypeEnum.REMOVE_WRINKLE;
    }

    @Override
    protected CreativeBatchVO buildData(RemoveWrinkleRequest request, MaterialModelVO modelVO) throws IOException {
        AssertUtil.assertNotBlank(request.getOriginImage(), "原始图片为空");

        CreativeBatchVO batch = new CreativeBatchVO();
        batch.setType(CreativeTypeEnum.REMOVE_WRINKLE);
        batch.setUserId(OperationContextHolder.getMasterUserId());
        batch.setShowImage(request.getOriginImage());
        batch.setBatchCnt(1);
        batch.setOperatorId(OperationContextHolder.getOperatorUserId());
        batch.setImageProportion("NONE");
        batch.setStatus(CreativeStatusEnum.QUEUE);
        batch.setExtInfo(CommonUtil.java2JSONObject(request));
        batchFillHelper.fillOriginBatchInfo(request.getOriginImage(), null, batch);
        removeWrinkleAsyncExecutor.storeSync(request, batch);
        return batch;
    }

    @Override
    protected boolean otherWithoutDeduction( RemoveWrinkleRequest request) {
        String image = request.getOriginImage();
        return Objects.nonNull(batchFillHelper.getTaskByUrl(image));
    }

    /**
     * 后置处理
     *
     * @param data    创作数据记录
     * @param request 请求
     */
    @Override
    protected void postProcess(CreativeBatchVO data, RemoveWrinkleRequest request) {
        CommonTaskVO task = removeWrinkleTaskHelper.createRemoveWrinkleTask(request.getOriginImage(), data, 0);
        AssertUtil.assertNotNull(task, "创建去皱任务失败");

        data.addExtInfo(CommonConstants.KEY_RELATED_COMMON_TASK, task.getId());
        data.addExtInfo(KEY_START_TIME, DateUtils.formatFullTime(new Date()));

        super.creativeBatchService.updateByIdSelective(data);
    }
}
