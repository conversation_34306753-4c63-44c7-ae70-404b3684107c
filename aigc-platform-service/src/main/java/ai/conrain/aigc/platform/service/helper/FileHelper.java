/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.helper;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import ai.conrain.aigc.platform.integration.ai.ComfyUIService;
import ai.conrain.aigc.platform.integration.ai.model.PromptResult;
import ai.conrain.aigc.platform.integration.aliyun.OssService;
import ai.conrain.aigc.platform.integration.aliyun.TairService;
import ai.conrain.aigc.platform.service.component.MaterialModelService;
import ai.conrain.aigc.platform.service.component.dispatch.FileDispatch;
import ai.conrain.aigc.platform.service.component.dispatch.ServerHelper;
import ai.conrain.aigc.platform.service.model.vo.MaterialModelVO;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import java.io.File;
import java.io.IOException;
import java.net.URLDecoder;
import java.nio.file.Files;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 文件帮助累
 *
 * <AUTHOR>
 * @version : FileHelper.java, v 0.1 2025/6/20 14:58 renxiao.wu Exp $
 */
@Slf4j
@Component
public class FileHelper {
    /** 文件同步锁前缀 */
    private static final String LOCK_PREFIX_FILE_SYNC = "_LOCK_FILE_SYNC_";
    /** 文件同步锁过期时间 */
    private static final int LOCK_EXPIRE_TIME = 20 * 60 * 1000;
    /** lora的正则 */
    private static final Pattern LORA_PATTERN = Pattern.compile(
        "lora_name: '([^']+\\.safetensors)' not in \\(list of length \\d+\\)");
    /** 图片的正则 */
    private static final Pattern IMAGE_PATTERN = Pattern.compile("image - Invalid image file: ([^\"]+)");

    @Value("${comfyui.input.path}")
    private String inputPath;
    @Value("${comfyui.lora.path}")
    private String loraRoot;
    @Value("${comfyui.root.path}")
    private String comfyuiRootPath;
    @Autowired
    private OssService ossService;
    @Autowired
    private FileDispatch fileDispatch;
    @Autowired
    private ComfyUIService comfyUIService;
    @Autowired
    private ServerHelper serverHelper;
    @Autowired
    private MaterialModelService materialModelService;
    @Autowired
    private TairService tairService;

    /**
     * 手动同步文件
     *
     * @param path    文件地址
     * @param fromUrl 文件来源
     * @return true，成功
     */
    public boolean syncFile(String path, String fromUrl) {
        //控制频次，间隔20分钟
        String lockKey = LOCK_PREFIX_FILE_SYNC + path;
        boolean lock = tairService.acquireLock(lockKey, LOCK_EXPIRE_TIME);
        if (!lock) {
            log.warn("手动同步文件，当前文件获取同步锁失败，跳过当前文件，path={}", path);
            return true;
        }

        try {

            String existsFileUrl = null;

            String absolutePath = path;
            String relativePath = path;
            boolean isLora = path.endsWith(".safetensors");
            if (isLora) {
                absolutePath = StringUtils.startsWith(path, comfyuiRootPath) ? path : loraRoot + path;
                relativePath = StringUtils.replace(relativePath, loraRoot, "");
            } else {
                absolutePath = StringUtils.startsWith(path, comfyuiRootPath) ? path : inputPath + path;
                relativePath = StringUtils.replace(path, inputPath, "");
            }

            //1.优先从设置的服务器上传文件
            if (StringUtils.isNotBlank(fromUrl)) {
                String fromFileUrl = serverHelper.getFileServerUrl(fromUrl);
                if (comfyUIService.checkFileExists(absolutePath, fromFileUrl)) {
                    existsFileUrl = fromFileUrl;
                    log.info("手动同步文件，文件{}在{}上存在1，开始通知同步文件", absolutePath, fromFileUrl);
                }
            }

            //2.如果源服务器上文件不存在，或者未传，则遍历所有文件服务
            if (StringUtils.isBlank(existsFileUrl)) {
                List<String> allFileServices = serverHelper.getAllFileServerUrl();
                for (String fileUrl : allFileServices) {
                    if (comfyUIService.checkFileExists(absolutePath, fileUrl)) {
                        log.info("手动同步文件，文件{}在{}上存在2，开始通知同步文件", absolutePath, fileUrl);
                        existsFileUrl = fileUrl;
                        break;
                    }
                }
            }

            //如果存在，则通知文件服务同步
            if (StringUtils.isNotBlank(existsFileUrl)) {
                fileDispatch.notifyFileSync(existsFileUrl, absolutePath, true);
                return true;
            }

            //3.如果所有文件服务器上都不存在
            //3.1.如果是lora文件，则从model中获取
            if (isLora) {
                MaterialModelVO model = materialModelService.queryByLoraName(relativePath);
                if (model == null) {
                    log.warn("手动同步文件,lora文件{}在model中不存在", absolutePath);
                    return false;
                }
                String ossUrl = model.getClothLoraTrainDetail().getLoraRetFileUrl();

                if (StringUtils.isNotBlank(ossUrl)) {
                    log.info("手动同步文件，文件{}在model中存在，开始通知同步文件", path);
                    Integer modelUserId = model.getUserId();

                    ossUrl = URLDecoder.decode(ossUrl, "UTF-8");
                    String ossPath = CommonUtil.getFilePathAndNameFromURL(ossUrl);
                    uploadByOss(ossPath, absolutePath, modelUserId);

                    return true;
                } else {
                    log.warn("手动同步文件,lora文件{}在model中不存在,modelId={}", absolutePath, model.getId());
                    return false;
                }
            }

            log.info("手动同步文件,文件{}在所有服务器中都不存在,开始从oss中获取", absolutePath);

            //3.2. 如果是图片文件，则直接从oss中获取
            Integer userId = OperationContextHolder.getMasterUserId();
            userId = userId != null ? userId : CommonUtil.mockSystemContext().getMasterUser();
            uploadByOss(relativePath, absolutePath, userId);

            log.warn("手动同步文件,文件{}从oss上下载后，通知下载", absolutePath);

            return true;
        } catch (Exception e) {
            log.error("手动同步文件异常,path=" + path, e);
            return false;
        } finally {
            tairService.releaseLock(lockKey);
        }
    }

    /**
     * 解析prompt结果，如果是文件缺失时则进行文件同步
     *
     * @param result prompt结果
     */
    public void parseAndSyncFile(PromptResult result) {
        // 不符合条件的都忽略
        if (result == null || result.isSuccess() || StringUtils.isBlank(result.getError()) || !CommonUtil.isValidJson(
            result.getError())) {
            return;
        }

        try {
            JSONObject json = JSONObject.parseObject(result.getError());
            for (String key : json.keySet()) {
                JSONObject node = json.getJSONObject(key);
                if (node == null || !node.containsKey("errors")) {
                    continue;
                }

                JSONArray errors = node.getJSONArray("errors");
                for (Object error : errors) {
                    if (!(error instanceof JSONObject)) {
                        continue;
                    }

                    parseByError((JSONObject)error);

                }
            }
        } catch (Exception e) {
            log.error("解析prompt结果异常，直接跳过，不影响业务", e);
        }
    }

    /**
     * 根据oss路径进行文件同步
     *
     * @param ossPath      oss路径
     * @param absolutePath 绝对路径
     * @param userId       用户id
     * @throws IOException io异常
     */
    private void uploadByOss(String ossPath, String absolutePath, Integer userId) throws IOException {
        String tmpPath = CommonUtil.getFileNameWithoutExtension(ossPath);
        String tmpUrl = ossService.downloadFile(ossPath, "/tmp/", tmpPath);

        File file = new File(tmpUrl);

        fileDispatch.uploadFile(absolutePath, Files.newInputStream(file.toPath()), userId);
    }

    /**
     * 基于异常信息进行解析
     *
     * @param errorJson 异常节点
     */
    private void parseByError(JSONObject errorJson) {
        String path = null;
        String details = errorJson.getString("details");

        Matcher matcher = LORA_PATTERN.matcher(details);

        if (matcher.find()) {
            path = matcher.group(1);
        } else {
            matcher = IMAGE_PATTERN.matcher(details);

            if (matcher.find()) {
                path = matcher.group(1);
            } else {
                log.warn("解析prompt结果, 不识别的错误信息{}", details);
            }
        }

        if (StringUtils.isNotBlank(path)) {
            log.info("解析prompt结果, 文件{}不存在,开始同步文件", path);
            syncFile(path, null);
        }
    }
}
