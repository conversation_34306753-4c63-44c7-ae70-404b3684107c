package ai.conrain.aigc.platform.service.component.impl;

import ai.conrain.aigc.platform.dal.example.ImageCaptionExample;
import ai.conrain.aigc.platform.dal.pgsql.dao.ImageCaptionDAO;
import ai.conrain.aigc.platform.dal.pgsql.entity.ImageCaptionDO;
import ai.conrain.aigc.platform.integration.ai.model.ImageAnalysisCaption;
import ai.conrain.aigc.platform.integration.aliyun.AliyunEmbeddingService;
import ai.conrain.aigc.platform.service.component.ImageCaptionService;
import ai.conrain.aigc.platform.service.enums.ClothGenderEnum;
import ai.conrain.aigc.platform.service.enums.ClothTypeEnum;
import ai.conrain.aigc.platform.service.model.biz.agent.StyleImageCandidate;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.converter.ImageCaptionConverter;
import ai.conrain.aigc.platform.service.model.query.ImageCaptionQuery;
import ai.conrain.aigc.platform.service.model.vo.ImageCaptionVO;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.service.util.VectorUtil;
import com.pgvector.PGvector;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

/**
 * ImageCaptionService实现
 *
 * <AUTHOR>
 * @version ImageCaptionService.java v 0.1 2025-07-25 11:33:21
 */
@Slf4j
@Service
public class ImageCaptionServiceImpl implements ImageCaptionService {

    /** DAO */
    @Autowired
    private ImageCaptionDAO imageCaptionDAO;

    @Autowired
    private AliyunEmbeddingService aliyunEmbeddingService;

    /** 内存缓存：存储所有ImageCaption数据 */
    private final Map<Integer, ImageCaptionVO> imageCaptionCache = new ConcurrentHashMap<>();

    /** 缓存初始化状态标识 */
    private final AtomicBoolean cacheInitialized = new AtomicBoolean(false);

    /** 缓存加载状态标识 */
    private final AtomicBoolean cacheLoading = new AtomicBoolean(false);

    @Override
    public ImageCaptionVO selectById(Integer id) {
        AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

        ImageCaptionDO data = imageCaptionDAO.selectByPrimaryKeyWithLogicalDelete(id, false);
        if (null == data) {
            return null;
        }

        return ImageCaptionConverter.do2VO(data);
    }

    @Override
    public void deleteById(Integer id) {
        AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

        int n = imageCaptionDAO.logicalDeleteByPrimaryKey(id);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "删除ImageCaption失败");
    }

    @Override
    public ImageCaptionVO insert(ImageCaptionVO imageCaption) {
        AssertUtil.assertNotNull(imageCaption, ResultCode.PARAM_INVALID, "imageCaption is null");
        AssertUtil.assertTrue(imageCaption.getId() == null, ResultCode.PARAM_INVALID, "imageCaption.id is present");

        // 创建时间、修改时间兜底
        if (imageCaption.getCreateTime() == null) {
            imageCaption.setCreateTime(new Date());
        }

        if (imageCaption.getModifyTime() == null) {
            imageCaption.setModifyTime(new Date());
        }

        ImageCaptionDO data = ImageCaptionConverter.vo2DO(imageCaption);
        // 逻辑删除字段初始化（虽然一般表字段有默认值）
        data.setDeleted(false);
        Integer n = imageCaptionDAO.insert(data);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "创建ImageCaption失败");
        AssertUtil.assertNotNull(data.getId(), "新建ImageCaption返回id为空");
        imageCaption.setId(data.getId());
        return imageCaption;
    }

    @Override
    public void updateByIdSelective(ImageCaptionVO imageCaption) {
        AssertUtil.assertNotNull(imageCaption, ResultCode.PARAM_INVALID, "imageCaption is null");
        AssertUtil.assertTrue(imageCaption.getId() != null, ResultCode.PARAM_INVALID, "imageCaption.id is null");

        // 使用反射验证所有PGvector类型的字段
        AssertUtil.validateDimensionOfNonNullPGVectorFields(imageCaption);

        // 修改时间必须更新
        imageCaption.setModifyTime(new Date());
        ImageCaptionDO data = ImageCaptionConverter.vo2DO(imageCaption);
        // 逻辑删除标过滤
        data.setDeleted(false);
        int n = imageCaptionDAO.updateByPrimaryKeySelective(data);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "更新ImageCaption失败，影响行数:" + n);
    }

    @Override
    public List<ImageCaptionVO> queryImageCaptionList(ImageCaptionQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        ImageCaptionExample example = ImageCaptionConverter.query2Example(query);

        List<ImageCaptionDO> list = imageCaptionDAO.selectByExample(example);
        return ImageCaptionConverter.doList2VOList(list);
    }

    @Override
    public List<ImageCaptionVO> queryImageCaptionList4Recall(ImageCaptionQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        ImageCaptionExample example = ImageCaptionConverter.query2Example(query);

        List<ImageCaptionDO> list = imageCaptionDAO.selectRecallVectorsByExample(example);
        return ImageCaptionConverter.doList2VOList(list);
    }

    @Override
    public Long queryImageCaptionCount(ImageCaptionQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        ImageCaptionExample example = ImageCaptionConverter.query2Example(query);
        return imageCaptionDAO.countByExample(example);
    }

    /**
     * 带条件分页查询图像标注
     */
    @Override
    public PageInfo<ImageCaptionVO> queryImageCaptionByPage(ImageCaptionQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        AssertUtil.assertTrue(query.getPageNum() != null && query.getPageSize() != null && query.getPageNum() >= 1
                && query.getPageSize() >= 1, ResultCode.PARAM_INVALID,
                "pageNum or pageSize is invalid,pageNum:"
                        + query.getPageNum() + ",pageSize:" + query.getPageSize());

        PageInfo<ImageCaptionVO> page = new PageInfo<>();

        ImageCaptionExample example = ImageCaptionConverter.query2Example(query);
        long totalCount = imageCaptionDAO.countByExample(example);
        if (totalCount == 0) {
            page.setList(new ArrayList<>());
            page.setSize(0);
            page.setTotalCount(0);
            page.setHasNextPage(false);

            return page;
        }

        List<ImageCaptionDO> list = imageCaptionDAO.selectByExample(example);
        page.setList(ImageCaptionConverter.doList2VOList(list));
        page.setSize(CollectionUtils.size(list));
        page.setTotalCount(totalCount);
        page.setHasNextPage(totalCount > (example.getOffset() + example.getRows()));

        return page;
    }

    /**
     * 根据款式向量相似度查询图像打标
     */
    @Override
    public List<ImageCaptionVO> queryByStyleVectorSimilarity(PGvector styleVector, ClothGenderEnum gender,
            double similarityThreshold, int limit, String genre) {

        AssertUtil.assertNotNull(styleVector, ResultCode.PARAM_INVALID, "styleVector is null");
        AssertUtil.assertNotNull(gender, ResultCode.PARAM_INVALID, "gender is null or blank");
        AssertUtil.assertTrue(similarityThreshold >= 0 && similarityThreshold <= 1,
                ResultCode.PARAM_INVALID, "similarityThreshold must be between 0 and 1");
        AssertUtil.assertTrue(limit > 0, ResultCode.PARAM_INVALID, "limit must be greater than 0");

        List<ImageCaptionDO> matchedImages = imageCaptionDAO.selectByStyleVector(styleVector, gender.getCode(),
                similarityThreshold, limit, genre);

        log.info("queryByStyleVectorSimilarity,gender:{},similarity:{},limit:{},genre:{}, matchedImages size:{}",
                gender.getCode(), similarityThreshold, limit, genre, matchedImages.size());

        return ImageCaptionConverter.doList2VOList(matchedImages);
    }

    @Override
    public PGvector calcClothTextVector(ImageAnalysisCaption clothAnalysis, ClothTypeEnum clothType) {
        String clothFeatureText = this.getClothFeatureTextByAnalysis(clothAnalysis, clothType);
        AssertUtil.assertNotBlank(clothFeatureText, ResultCode.PARAM_INVALID, "clothFeatureText is null or blank");
        return aliyunEmbeddingService.getTextEmbeddingByMultiModalModel(clothFeatureText);
    }

    @Override
    public void fillSortVectors(List<StyleImageCandidate> candidates) {
        AssertUtil.assertNotEmpty(candidates, ResultCode.PARAM_INVALID, "candidates is null");
        List<Integer> ids = candidates.stream().map(c -> c.getImageCaption().getId()).toList();
        AssertUtil.assertNotEmpty(ids, ResultCode.PARAM_INVALID, "candidates caption is empty");

        List<ImageCaptionDO> sortVectors = imageCaptionDAO.selectSortVectors(ids);
        Map<Integer, ImageCaptionDO> sortVectorMap = sortVectors.stream().collect(Collectors.toMap(ImageCaptionDO::getId, c -> c));
        candidates.forEach(c -> {
            ImageCaptionVO cap = c.getImageCaption();
            ImageCaptionDO target = sortVectorMap.get(cap.getId());
            if (target != null) {
                cap.setImgEmb(target.getImgEmb());
                cap.setBgImgEmb(target.getBgImgEmb());
                cap.setModelFacialImgEmb(target.getModelFacialImgEmb());
                cap.setModelPoseImgEmb(target.getModelPoseImgEmb());
                cap.setSortBgTextEmb(target.getSortBgTextEmb());
                cap.setSortFacialExpressionTextEmb(target.getSortFacialExpressionTextEmb());
                cap.setSortAccessoriesTextEmb(target.getSortAccessoriesTextEmb());
                cap.setSortPoseTextEmb(target.getSortPoseTextEmb());
            }

            c.setImageCaption(cap);
        });
    }

    /**
     * 根据结构化描述构造服装文本
     *
     * @param clothAnalysis 服装分析数据
     * @param clothType     服装类型
     * @return 格式化的服装描述文本
     */
    @Override
    public String getClothFeatureTextByAnalysis(ImageAnalysisCaption clothAnalysis, ClothTypeEnum clothType) {
        if (clothAnalysis == null) {
            log.error("服装分析数据为空，无法构造服装文本");
            return "";
        }

        if (clothType == null) {
            log.error("服装类型为空，无法构造服装文本");
            return "";
        }

        ImageAnalysisCaption.Clothing clothing = clothAnalysis.getClothing();
        List<String> descriptionParts = new ArrayList<>();

        // 根据服装类型决定提取哪些信息
        String targetClothingType = mapClothTypeToTarget(clothType);

        // --- 提取上衣信息 ---
        if (("Top".equals(targetClothingType) || "Suit".equals(targetClothingType)) &&
                clothing.getTop() != null && isValidValue(clothing.getTop().getType())) {

            ImageAnalysisCaption.Top top = clothing.getTop();
            List<String> topDescriptionElements = new ArrayList<>();

            if (isValidValue(top.getColor())) {
                topDescriptionElements.add("a " + top.getColor() + "-colored");
            }
            if (isValidValue(top.getLength())) {
                topDescriptionElements.add(top.getLength());
            }
            if (isValidValue(top.getType())) {
                topDescriptionElements.add(top.getType());
            }
            if (isValidValue(top.getFit())) {
                topDescriptionElements.add(", with a " + top.getFit() + " fit");
            }
            if (isValidValue(top.getSleeveLength())) {
                topDescriptionElements.add(", " + top.getSleeveLength());
            }
            if (isValidValue(top.getStyle())) {
                topDescriptionElements.add(", style description: " + top.getStyle());
            }
            if (isValidValue(top.getPatternAndFeature())) {
                topDescriptionElements.add(", features: " + top.getPatternAndFeature());
            }

            if (!topDescriptionElements.isEmpty()) {
                descriptionParts.add("Top: " + String.join("", topDescriptionElements));
            }
        }

        // --- 提取下装信息 ---
        if (("Bottom".equals(targetClothingType) || "Suit".equals(targetClothingType)) &&
                clothing.getBottom() != null && isValidValue(clothing.getBottom().getType())) {

            ImageAnalysisCaption.Bottom bottom = clothing.getBottom();
            List<String> bottomDescriptionElements = new ArrayList<>();

            if (isValidValue(bottom.getColor())) {
                bottomDescriptionElements.add("a " + bottom.getColor() + "-colored");
            }
            if (isValidValue(bottom.getLength())) {
                bottomDescriptionElements.add(bottom.getLength());
            }
            if (isValidValue(bottom.getType())) {
                bottomDescriptionElements.add(bottom.getType());
            }
            if (isValidValue(bottom.getStyle())) {
                bottomDescriptionElements.add(", style description: " + bottom.getStyle());
            }
            if (isValidValue(bottom.getPatternAndFeature())) {
                bottomDescriptionElements.add(", features: " + bottom.getPatternAndFeature());
            }

            if (!bottomDescriptionElements.isEmpty()) {
                descriptionParts.add("Bottom: " + String.join("", bottomDescriptionElements));
            }
        }

        // 返回结果
        if (descriptionParts.isEmpty()) {
            log.error("No information found for the specified clothing type: {}.", targetClothingType);
            return null;
        } else {
            return String.join(" ", descriptionParts);
        }
    }

    @Override
    public String getClothStyleDescription(ImageAnalysisCaption captionModel) {

        // 从结构化描述中提取款式相关字段
        if (captionModel == null) {
            return "";
        }

        StringBuilder styleDesc = new StringBuilder();
        if (captionModel.getClothing().getTop() != null) {
            ImageAnalysisCaption.Top top = captionModel.getClothing().getTop();
            styleDesc.append("Top: ").append(top.getStyle()).append(" ");
        }

        if (captionModel.getClothing().getBottom() != null) {
            ImageAnalysisCaption.Bottom bottom = captionModel.getClothing().getBottom();
            styleDesc.append("Bottom: ").append(bottom.getStyle()).append(" ");
        }

        return styleDesc.toString().trim();
    }

    /**
     * 将 ClothTypeEnum 映射为目标服装类型字符串
     */
    private String mapClothTypeToTarget(ClothTypeEnum clothType) {
        if (clothType == null) {
            return null;
        }

        switch (clothType) {
            case Tops:
                return "Top";
            case Bottoms:
                return "Bottom";
            case TwoPiece:
            case SwimSuit:
            case OnePiece:
            case SexyLingerie:
                return "Suit";
            default:
                return null;
        }
    }

    /**
     * 检查值是否有效（非空且不为"None"）
     */
    private boolean isValidValue(String value) {
        return value != null && !value.trim().isEmpty() && !"null".equalsIgnoreCase(value.trim());
    }

    /**
     * 初始化缓存
     */
    @Override
    public void initCacheOnStartup() {
        // 缓存已经初始化完成，直接返回
        if (cacheInitialized.get()) {
            log.info("ImageCaption缓存已经初始化完成，跳过重复初始化");
            return;
        }

        // 确保只有一个线程能执行初始化
        if (cacheLoading.compareAndSet(false, true)) {
            try {
                // 双重检查，防止在获取锁的过程中其他线程已经完成初始化
                if (cacheInitialized.get()) {
                    log.info("ImageCaption缓存已经初始化完成，跳过重复初始化");
                    return;
                }

                // 记录初始内存使用情况
                Runtime runtime = Runtime.getRuntime();
                long initialUsedMemory = runtime.totalMemory() - runtime.freeMemory();

                log.info("开始异步加载ImageCaption数据到内存缓存");
                long startTime = System.currentTimeMillis();

                // 分批加载数据，避免一次性加载过多数据导致内存溢出
                int batchSize = 2000;
                int offset = 0;
                int totalLoaded = 0;

                while (true) {
                    ImageCaptionQuery query = new ImageCaptionQuery();
                    query.setPageNum((offset / batchSize) + 1);
                    query.setPageSize(batchSize);

                    List<ImageCaptionVO> batch = queryImageCaptionList4Recall(query);
                    if (CollectionUtils.isEmpty(batch)) {
                        break;
                    }

                    // 将数据加载到缓存
                    for (ImageCaptionVO caption : batch) {
                        if (caption.getId() != null) {
                            imageCaptionCache.put(caption.getId(), caption);
                        }
                    }

                    totalLoaded += batch.size();
                    offset += batchSize;

                    // 如果批次数据少于batchSize，说明已经加载完毕
                    if (batch.size() < batchSize) {
                        break;
                    }
                }

                cacheInitialized.set(true);

                // 记录缓存初始化完成后的内存使用情况
                long finalUsedMemory = runtime.totalMemory() - runtime.freeMemory();
                long cacheMemoryUsage = finalUsedMemory - initialUsedMemory;
                long endTime = System.currentTimeMillis();
                log.info("ImageCaption缓存初始化完成，共加载{}条数据，耗时{}ms，缓存占用内存: {}MB，当前总内存使用: {}MB",
                        totalLoaded,
                        endTime - startTime,
                        String.format("%.2f", cacheMemoryUsage / (1024.0 * 1024.0)),
                        String.format("%.2f", finalUsedMemory / (1024.0 * 1024.0)));

            } catch (Exception e) {
                log.error("ImageCaption缓存初始化失败", e);
            } finally {
                cacheLoading.set(false);
            }
        }
    }

    /**
     * 从缓存中查询ImageCaption数据（优化版本的queryByStyleVectorSimilarity）
     * 如果缓存未初始化，则回退到数据库查询
     */
    public List<ImageCaptionVO> queryByStyleVectorSimilarityFromCache(PGvector styleVector, ClothGenderEnum gender,
            double similarityThreshold, int limit, String genre) {
        // 如果缓存未初始化，回退到数据库查询
        if (!cacheInitialized.get()) {
            log.info("queryByStyleVectorSimilarityFromCache缓存未初始化，使用数据库查询");
            return queryByStyleVectorSimilarity(styleVector, gender, similarityThreshold, limit, genre);
        }

        try {
            List<ImageCaptionVO> candidates = new ArrayList<>();

            // 从缓存中筛选符合条件的数据
            for (ImageCaptionVO caption : imageCaptionCache.values()) {
                // 检查基本条件
                if (!gender.getCode().equals(caption.getClothGenderType()) ||
                        !"scene".equals(caption.getImageType())) {
                    continue;
                }

                // 检查流派条件
                if (genre != null && caption.getCaption() != null) {
                    String captionGenre = null;
                    try {
                        if (caption.getCaption().getShootingTheme() != null) {
                            captionGenre = caption.getCaption().getShootingTheme().getGenre();
                        }
                    } catch (Exception e) {
                        log.debug("解析caption流派信息失败: {}", e.getMessage());
                    }

                    if (!genre.equals(captionGenre)) {
                        continue;
                    }
                }

                // 计算向量相似度
                if (caption.getClothStyleTextEmb() != null) {
                    try {
                        double similarity = VectorUtil.innerProduct(styleVector, caption.getClothStyleTextEmb());
                        if (similarity >= similarityThreshold) {
                            caption.setClothStyleSimilarity(similarity);
                            candidates.add(caption);
                        }
                    } catch (Exception e) {
                        log.debug("计算向量相似度失败: {}", e.getMessage());
                    }
                }
            }

            // 按相似度排序并限制数量
            candidates.sort((a, b) -> Double.compare(
                    b.getClothStyleSimilarity() != null ? b.getClothStyleSimilarity() : 0.0,
                    a.getClothStyleSimilarity() != null ? a.getClothStyleSimilarity() : 0.0));

            if (candidates.size() > limit) {
                candidates = candidates.subList(0, limit);
            }

            log.info("从缓存查询到{}条匹配数据，gender:{}, similarity:{}, limit:{}, genre:{}",
                    candidates.size(), gender.getCode(), similarityThreshold, limit, genre);

            return candidates;

        } catch (Exception e) {
            log.error("从缓存查询数据失败，回退到数据库查询", e);
            return queryByStyleVectorSimilarity(styleVector, gender, similarityThreshold, limit, genre);
        }
    }

    /**
     * 等待缓存初始化完成（仅用于测试）
     * @param maxWaitSeconds 最大等待时间（秒）
     * @return 是否初始化完成
     */
    public boolean waitForCacheInitialization(int maxWaitSeconds) {
        if (cacheInitialized.get()) {
            return true;
        }
        
        log.info("线程等待ImageCaption缓存初始化完成，最大等待时间: {} 秒", maxWaitSeconds);
        
        int waitedSeconds = 0;
        while (!cacheInitialized.get() && waitedSeconds < maxWaitSeconds) {
            try {
                Thread.sleep(1000); // 每秒检查一次
                waitedSeconds++;
                
                if (waitedSeconds % 10 == 0) {
                    log.info("线程已等待缓存初始化 {} 秒", waitedSeconds);
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.warn("线程在等待缓存初始化时被中断");
                return false;
            }
        }
        
        boolean initialized = cacheInitialized.get();
        if (initialized) {
            log.info("线程等待缓存初始化完成，总等待时间: {} 秒", waitedSeconds);
        } else {
            log.warn("线程等待缓存初始化超时，等待时间: {} 秒", waitedSeconds);
        }
        
        return initialized;
    }
}